'use client';

import { useState, useEffect } from 'react';

export default function TestGameLoader() {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (test, status, message) => {
    setTestResults(prev => [...prev, { test, status, message, timestamp: new Date().toLocaleTimeString() }]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    // Test 1: Check if localStorage is available
    try {
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      addResult('localStorage availability', 'PASS', 'localStorage is available');
    } catch (error) {
      addResult('localStorage availability', 'FAIL', `localStorage error: ${error.message}`);
      setIsRunning(false);
      return;
    }

    // Test 2: Test image storage and retrieval
    try {
      const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      const testChatId = 'test123';
      const imageKey = `gameImage_${testChatId}`;
      
      localStorage.setItem(imageKey, testImageBase64);
      const retrieved = localStorage.getItem(imageKey);
      
      if (retrieved === testImageBase64) {
        addResult('Image storage/retrieval', 'PASS', 'Image successfully stored and retrieved from localStorage');
      } else {
        addResult('Image storage/retrieval', 'FAIL', 'Retrieved image does not match stored image');
      }
      
      localStorage.removeItem(imageKey);
    } catch (error) {
      addResult('Image storage/retrieval', 'FAIL', `Image storage error: ${error.message}`);
    }

    // Test 3: Test game data storage
    try {
      const testGameData = {
        chatId: 'test123',
        backgroundImageUrl: 'https://example.com/test.jpg',
        questions: [
          { title: 'Test Question', question: 'What if...?', options: ['A', 'B'] }
        ]
      };
      
      localStorage.setItem('storyGameData', JSON.stringify(testGameData));
      const retrievedData = JSON.parse(localStorage.getItem('storyGameData'));
      
      if (retrievedData.chatId === testGameData.chatId) {
        addResult('Game data storage', 'PASS', 'Game data successfully stored and retrieved');
      } else {
        addResult('Game data storage', 'FAIL', 'Retrieved game data does not match');
      }
      
      localStorage.removeItem('storyGameData');
    } catch (error) {
      addResult('Game data storage', 'FAIL', `Game data storage error: ${error.message}`);
    }

    // Test 4: Test blob to base64 conversion
    try {
      const testBlob = new Blob(['test data'], { type: 'text/plain' });
      const base64 = await blobToBase64(testBlob);
      
      if (base64.startsWith('data:text/plain;base64,')) {
        addResult('Blob to base64 conversion', 'PASS', 'Blob successfully converted to base64');
      } else {
        addResult('Blob to base64 conversion', 'FAIL', 'Invalid base64 format');
      }
    } catch (error) {
      addResult('Blob to base64 conversion', 'FAIL', `Conversion error: ${error.message}`);
    }

    // Test 5: Test CORS-safe image loading simulation
    try {
      // Simulate the game-loader flow
      const mockImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      const response = await fetch(mockImageUrl);
      const blob = await response.blob();
      const base64 = await blobToBase64(blob);
      
      if (base64.startsWith('data:image/')) {
        addResult('CORS-safe image loading', 'PASS', 'Image successfully loaded and converted');
      } else {
        addResult('CORS-safe image loading', 'FAIL', 'Invalid image format');
      }
    } catch (error) {
      addResult('CORS-safe image loading', 'FAIL', `Loading error: ${error.message}`);
    }

    setIsRunning(false);
  };

  const blobToBase64 = (blob) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  };

  const clearLocalStorage = () => {
    const keys = Object.keys(localStorage);
    const gameKeys = keys.filter(key => 
      key.startsWith('gameImage_') || 
      key === 'gameBackgroundImage' || 
      key === 'storyGameData' || 
      key === 'currentChatId'
    );
    
    gameKeys.forEach(key => localStorage.removeItem(key));
    addResult('Cleanup', 'INFO', `Cleared ${gameKeys.length} game-related localStorage keys`);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Game Loader Test Suite</h1>
        
        <div className="mb-6 space-x-4">
          <button
            onClick={runTests}
            disabled={isRunning}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? 'Running Tests...' : 'Run Tests'}
          </button>
          
          <button
            onClick={clearLocalStorage}
            className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            Clear localStorage
          </button>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          
          {testResults.length === 0 ? (
            <p className="text-gray-400">No tests run yet. Click "Run Tests" to start.</p>
          ) : (
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border-l-4 ${
                    result.status === 'PASS'
                      ? 'bg-green-900/20 border-green-500 text-green-100'
                      : result.status === 'FAIL'
                      ? 'bg-red-900/20 border-red-500 text-red-100'
                      : 'bg-blue-900/20 border-blue-500 text-blue-100'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <span className="font-semibold">{result.test}</span>
                      <span className={`ml-2 px-2 py-1 text-xs rounded ${
                        result.status === 'PASS'
                          ? 'bg-green-600'
                          : result.status === 'FAIL'
                          ? 'bg-red-600'
                          : 'bg-blue-600'
                      }`}>
                        {result.status}
                      </span>
                    </div>
                    <span className="text-xs text-gray-400">{result.timestamp}</span>
                  </div>
                  <p className="mt-1 text-sm">{result.message}</p>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="mt-6 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">localStorage Contents</h2>
          <div className="text-sm">
            <pre className="bg-gray-900 p-4 rounded overflow-auto">
              {typeof window !== 'undefined' ? JSON.stringify(
                Object.keys(localStorage).reduce((acc, key) => {
                  if (key.startsWith('game') || key === 'storyGameData' || key === 'currentChatId') {
                    acc[key] = localStorage.getItem(key)?.substring(0, 100) + '...';
                  }
                  return acc;
                }, {}),
                null,
                2
              ) : 'Loading...'}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
