'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import { ArrowLeft, Save, RefreshCw } from 'lucide-react';

// Extract Book Information utility (moved from utils)
const extractBookInformation = (analysisResults) => {
  if (!analysisResults || !Array.isArray(analysisResults) || analysisResults.length === 0) {
    return {
      bookTitle: 'Unknown Book',
      author: 'Unknown Author',
      changeLocation: '',
      originalEvent: ''
    };
  }

  // Get the highest-ranked chunk for book information
  const topChunk = analysisResults[0];

  // Extract book title and author from the chunk path or title
  let bookTitle = 'Unknown Book';
  let author = 'Unknown Author';

  if (topChunk.path && topChunk.path.length > 0) {
    // Try to extract from path
    const pathString = topChunk.path.join(' > ');
    bookTitle = pathString.split(' > ')[0] || topChunk.title || 'Unknown Book';
  } else {
    bookTitle = topChunk.title || 'Unknown Book';
  }

  // Try to extract author from content or title
  const content = topChunk.content || '';
  const authorMatch = content.match(/(?:by|author|written by)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i);
  if (authorMatch) {
    author = authorMatch[1];
  }

  return {
    bookTitle: bookTitle,
    author: author,
    changeLocation: topChunk.title || '',
    originalEvent: content.substring(0, 200) + '...'
  };
};

export default function AlternateScenarioPage() {
  const [isGenerating, setIsGenerating] = useState(true);
  const [gameData, setGameData] = useState(null);
  const [error, setError] = useState('');
  const [userPrompt, setUserPrompt] = useState('');
  const [bookInfo, setBookInfo] = useState({
    bookTitle: '',
    author: '',
    changeLocation: '',
    originalEvent: ''
  });

  // API integration state
  const [currentChatId, setCurrentChatId] = useState(null);
  const [isLoadingFromAPI, setIsLoadingFromAPI] = useState(false);
  const [isSavingStoryData, setIsSavingStoryData] = useState(false);
  const [storyDataId, setStoryDataId] = useState(null);
  const [hasExistingStoryData, setHasExistingStoryData] = useState(false);

  // Function to validate story data format for Godot
  const validateStoryData = (data) => {
    if (!data) {
      console.error('Story data is null or undefined');
      return false;
    }

    if (!data.questions || !Array.isArray(data.questions)) {
      console.error('Story data missing questions array:', data);
      return false;
    }

    if (data.questions.length === 0) {
      console.error('Story data has empty questions array');
      return false;
    }

    // Check if first question has required fields
    const firstQuestion = data.questions[0];
    if (!firstQuestion.question || !firstQuestion.options) {
      console.error('First question missing required fields:', firstQuestion);
      return false;
    }

    console.log('Story data validation passed:', {
      questionsCount: data.questions.length,
      firstQuestion: {
        title: firstQuestion.title,
        hasQuestion: !!firstQuestion.question,
        optionsCount: firstQuestion.options?.length || 0,
        level: firstQuestion.level
      }
    });

    return true;
  };

  // Base64 image processing removed - Godot now handles Firebase Storage URLs directly

  // Test Firebase Storage image loading
  const testFirebaseImageLoading = async (imageUrl) => {
    if (!imageUrl) {
      console.log('🖼️ No background image URL to test');
      return;
    }

    console.log('🧪 Testing Firebase Storage image loading...');
    console.log('🔗 Image URL:', imageUrl);

    try {
      // Test 1: Basic fetch
      console.log('📡 Testing basic fetch...');
      const response = await fetch(imageUrl);
      console.log('📊 Response status:', response.status);
      console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        console.error('❌ Fetch failed:', response.status, response.statusText);
        return;
      }

      // Test 2: Get blob data
      console.log('📦 Testing blob conversion...');
      const blob = await response.blob();
      console.log('📊 Blob size:', blob.size, 'bytes');
      console.log('📊 Blob type:', blob.type);

      // Test 3: Create object URL and test image loading
      console.log('🖼️ Testing image element loading...');
      const objectUrl = URL.createObjectURL(blob);

      const img = new Image();
      img.onload = () => {
        console.log('✅ Image loaded successfully!');
        console.log('📊 Image dimensions:', img.width, 'x', img.height);
        URL.revokeObjectURL(objectUrl);
      };
      img.onerror = (error) => {
        console.error('❌ Image element failed to load:', error);
        URL.revokeObjectURL(objectUrl);
      };
      img.src = objectUrl;

      // Test 4: Test CORS headers
      const corsHeaders = {
        'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
        'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
        'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
      };
      console.log('🌐 CORS headers:', corsHeaders);

    } catch (error) {
      console.error('❌ Firebase Storage image test failed:', error);
    }
  };

  // Godot Image Loading Bridge - provides image loading capabilities for Godot
  const setupGodotImageBridge = () => {
    // Create a global image loading service for Godot
    window.GodotImageLoader = {
      // Cache for loaded images
      imageCache: new Map(),

      // Load image from Firebase Storage URL and return as base64
      loadImageAsBase64: async (imageUrl, retries = 3) => {
        try {
          console.log('🌉 Godot Bridge: Loading image as base64:', imageUrl);

          // Check cache first
          if (window.GodotImageLoader.imageCache.has(imageUrl)) {
            console.log('📦 Godot Bridge: Returning cached image');
            return window.GodotImageLoader.imageCache.get(imageUrl);
          }

          // Retry logic
          let lastError;
          for (let attempt = 1; attempt <= retries; attempt++) {
            try {
              console.log(`🔄 Godot Bridge: Attempt ${attempt}/${retries}`);

              // Fetch image with timeout using Promise.race (universal compatibility)
              console.log(`🔄 Godot Bridge: Fetching image (attempt ${attempt}/${retries})`);

              const response = await Promise.race([
                fetch(imageUrl, {
                  headers: {
                    'Cache-Control': 'no-cache'
                  }
                }),
                new Promise((_, reject) =>
                  setTimeout(() => reject(new Error('Request timeout after 10 seconds')), 10000)
                )
              ]);

              if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
              }

              // Convert to blob
              const blob = await response.blob();

              // Validate blob
              if (blob.size === 0) {
                throw new Error('Received empty image data');
              }

              if (!blob.type.startsWith('image/')) {
                throw new Error(`Invalid content type: ${blob.type}`);
              }

              // Convert blob to base64
              return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                  const base64 = reader.result;
                  // Cache the result
                  window.GodotImageLoader.imageCache.set(imageUrl, base64);
                  console.log('✅ Godot Bridge: Image loaded and cached');
                  resolve(base64);
                };
                reader.onerror = () => reject(new Error('Failed to convert blob to base64'));
                reader.readAsDataURL(blob);
              });

            } catch (error) {
              lastError = error;
              console.warn(`⚠️ Godot Bridge: Attempt ${attempt} failed:`, error.message);

              if (attempt < retries) {
                // Wait before retry (exponential backoff)
                const delay = Math.pow(2, attempt - 1) * 1000;
                console.log(`⏳ Godot Bridge: Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
              }
            }
          }

          throw lastError;

        } catch (error) {
          console.error('❌ Godot Bridge: Failed to load image after all retries:', error);
          throw error;
        }
      },

      // Load image and return as object URL
      loadImageAsObjectURL: async (imageUrl) => {
        try {
          console.log('🌉 Godot Bridge: Loading image as object URL:', imageUrl);

          // Use simple fetch for object URL creation (no need for complex retry logic here)
          const response = await fetch(imageUrl);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const blob = await response.blob();
          const objectUrl = URL.createObjectURL(blob);

          console.log('✅ Godot Bridge: Object URL created:', objectUrl);
          return objectUrl;

        } catch (error) {
          console.error('❌ Godot Bridge: Failed to create object URL:', error);
          throw error;
        }
      },

      // Preload background image for current story
      preloadBackgroundImage: async () => {
        try {
          const storyData = localStorage.getItem('storyGameData');
          if (!storyData) {
            console.log('📄 Godot Bridge: No story data available');
            return null;
          }

          const parsed = JSON.parse(storyData);

          // First, try to load from localStorage (pre-loaded by game-loader)
          const chatId = parsed.chatId || localStorage.getItem('currentChatId');
          if (chatId) {
            const imageKey = `gameImage_${chatId}`;
            const localStorageImage = localStorage.getItem(imageKey) || localStorage.getItem('gameBackgroundImage');

            if (localStorageImage) {
              console.log('✅ Godot Bridge: Loading image from localStorage (CORS-safe)');
              window.preloadedBackgroundImage = localStorageImage;
              console.log('✅ Godot Bridge: Background image loaded from localStorage and available globally');
              return localStorageImage;
            }
          }

          // Fallback to Firebase Storage URL if localStorage doesn't have the image
          if (!parsed.backgroundImageUrl) {
            console.log('🖼️ Godot Bridge: No background image URL in story data and no localStorage image');
            return null;
          }

          console.log('🚀 Godot Bridge: Fallback to Firebase Storage (may have CORS issues)...');
          const base64 = await window.GodotImageLoader.loadImageAsBase64(parsed.backgroundImageUrl);

          // Store preloaded image in a global variable for Godot to access
          window.preloadedBackgroundImage = base64;
          console.log('✅ Godot Bridge: Background image preloaded from Firebase and available globally');

          return base64;
        } catch (error) {
          console.error('❌ Godot Bridge: Failed to preload background image:', error);

          // Try to load fallback image
          try {
            console.log('🔄 Godot Bridge: Attempting to load fallback image...');
            const fallbackBase64 = await window.GodotImageLoader.loadFallbackImage();
            if (fallbackBase64) {
              window.preloadedBackgroundImage = fallbackBase64;
              console.log('✅ Godot Bridge: Fallback image loaded');
              return fallbackBase64;
            }
          } catch (fallbackError) {
            console.error('❌ Godot Bridge: Fallback image also failed:', fallbackError);
          }

          return null;
        }
      },

      // Load fallback image (default background)
      loadFallbackImage: async () => {
        try {
          // Create a simple colored background as fallback
          const canvas = document.createElement('canvas');
          canvas.width = 1920;
          canvas.height = 1080;
          const ctx = canvas.getContext('2d');

          // Create a gradient background
          const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
          gradient.addColorStop(0, '#1a1a2e');
          gradient.addColorStop(0.5, '#16213e');
          gradient.addColorStop(1, '#0f3460');

          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Add some decorative elements
          ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
          for (let i = 0; i < 50; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const radius = Math.random() * 3 + 1;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
          }

          // Convert to base64
          const base64 = canvas.toDataURL('image/png');
          console.log('✅ Godot Bridge: Generated fallback background');
          return base64;

        } catch (error) {
          console.error('❌ Godot Bridge: Failed to generate fallback image:', error);
          return null;
        }
      },

      // Load image directly from localStorage (CORS-safe)
      loadImageFromLocalStorage: (chatId = null) => {
        try {
          // Try specific chatId key first
          if (chatId) {
            const imageKey = `gameImage_${chatId}`;
            const image = localStorage.getItem(imageKey);
            if (image) {
              console.log('✅ Godot Bridge: Image loaded from localStorage with chatId key');
              return image;
            }
          }

          // Try general key as fallback
          const generalImage = localStorage.getItem('gameBackgroundImage');
          if (generalImage) {
            console.log('✅ Godot Bridge: Image loaded from localStorage with general key');
            return generalImage;
          }

          console.log('📭 Godot Bridge: No image found in localStorage');
          return null;
        } catch (error) {
          console.error('❌ Godot Bridge: Error loading from localStorage:', error);
          return null;
        }
      },

      // Get current background image (preloaded or fallback)
      getCurrentBackgroundImage: () => {
        return window.preloadedBackgroundImage || null;
      },

      // Clear image cache
      clearCache: () => {
        window.GodotImageLoader.imageCache.clear();
        window.preloadedBackgroundImage = null;
        console.log('🧹 Godot Bridge: Cache cleared');
      },

      // Get status information
      getStatus: () => {
        const storyData = localStorage.getItem('storyGameData');
        let parsedData = null;
        try {
          parsedData = storyData ? JSON.parse(storyData) : null;
        } catch (e) {
          // ignore parse errors
        }

        const chatId = parsedData?.chatId || localStorage.getItem('currentChatId');
        const localStorageImage = window.GodotImageLoader.loadImageFromLocalStorage(chatId);

        return {
          bridgeAvailable: !!window.GodotImageLoader,
          storyDataAvailable: !!storyData,
          backgroundImageUrl: parsedData?.backgroundImageUrl || null,
          preloadedImageAvailable: !!window.preloadedBackgroundImage,
          localStorageImageAvailable: !!localStorageImage,
          chatId: chatId,
          cacheSize: window.GodotImageLoader.imageCache.size,
          cachedUrls: Array.from(window.GodotImageLoader.imageCache.keys())
        };
      },

      // Validate image URL
      validateImageUrl: (url) => {
        if (!url || typeof url !== 'string') {
          return { valid: false, reason: 'URL is empty or not a string' };
        }

        if (!url.startsWith('https://firebasestorage.googleapis.com/')) {
          return { valid: false, reason: 'URL is not a Firebase Storage URL' };
        }

        if (!url.includes('background%2F')) {
          return { valid: false, reason: 'URL does not point to background folder' };
        }

        if (!url.includes('alt=media')) {
          return { valid: false, reason: 'URL missing media parameter' };
        }

        return { valid: true, reason: 'URL appears valid' };
      }
    };

    console.log('🌉 Godot Image Bridge initialized');
  };

  // Check if we're in the browser
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);

    // Setup Godot Image Bridge
    setupGodotImageBridge();

    // Add debug function to window for troubleshooting
    window.debugStoryData = () => {
      const storyData = localStorage.getItem('storyGameData');
      console.log('=== Story Data Debug ===');
      console.log('localStorage storyGameData:', storyData);
      console.log('window.storyGameData:', window.storyGameData);
      console.log('Parsed data:', storyData ? JSON.parse(storyData) : null);
      console.log('========================');
      return storyData ? JSON.parse(storyData) : null;
    };
  }, []);

  // Canvas-related state and refs
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [gameLoaded, setGameLoaded] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [gameError, setGameError] = useState(null);
  const engineRef = useRef(null);







  // Load data and generate scenario
  useEffect(() => {
    if (!isClient) return;

    let isMounted = true; // Flag to prevent state updates if component unmounts

    async function loadDataAndGenerateScenario() {
      try {
        // First try to get chat ID from URL or localStorage
        let chatId = null;

        // Check URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const urlChatId = urlParams.get('chatId');

        // Check localStorage
        const savedChatId = localStorage.getItem('currentChatId');

        chatId = urlChatId || savedChatId;

        if (chatId && chatId !== '') {
          console.log('Loading data from chat:', chatId);
          if (isMounted) setCurrentChatId(chatId);

          // Load chat data
          try {
            if (isMounted) setIsLoadingFromAPI(true);

            // Fetch chat messages to get analysis results
            const messagesResponse = await fetch(`/api/chats/${chatId}/messages`);
            if (!messagesResponse.ok) {
              throw new Error('Failed to load chat messages');
            }

            const messagesData = await messagesResponse.json();
            const messages = messagesData.messages || [];

            // Find analysis results and user prompt
            const userQuery = messages.find(msg => msg.type === 'user_query');
            const analysisMessage = messages.find(msg => msg.type === 'analysis_results');

            let loadedPrompt = '';
            let loadedBookInfo = {};

            if (userQuery) {
              loadedPrompt = userQuery.content;
            }

            if (analysisMessage) {
              try {
                const parsedResults = JSON.parse(analysisMessage.content);

                // Extract book information
                const extractedBookInfo = extractBookInformation(parsedResults);
                loadedBookInfo = extractedBookInfo;

                console.log('Loaded analysis results from chat:', parsedResults.length, 'results');
              } catch (parseError) {
                console.error('Error parsing analysis results:', parseError);
                throw new Error('Invalid analysis results format');
              }
            }

            // First check if we have fresh story data from the prompt page
            const freshStoryData = localStorage.getItem('storyGameData');
            if (freshStoryData) {
              try {
                const parsedFreshData = JSON.parse(freshStoryData);
                if (parsedFreshData && parsedFreshData.questions && parsedFreshData.questions.length > 0) {
                  console.log('🎮 Found fresh story data from prompt page, using it directly');
                  console.log('🖼️ Fresh data has background image URL:', !!parsedFreshData.backgroundImageUrl);
                  console.log('🔗 Fresh background image URL:', parsedFreshData.backgroundImageUrl || 'none');

                  // Test Firebase Storage image loading
                  if (parsedFreshData.backgroundImageUrl) {
                    testFirebaseImageLoading(parsedFreshData.backgroundImageUrl);
                  }

                  if (isMounted) {
                    setGameData(parsedFreshData);
                    setIsGenerating(false);
                    setUserPrompt(loadedPrompt);
                    setBookInfo(loadedBookInfo);
                  }

                  // Validate and ensure fresh story data is available globally
                  if (validateStoryData(parsedFreshData)) {
                    window.storyGameData = parsedFreshData;
                    console.log('✅ Fresh story data loaded and made available globally');
                    console.log('🖼️ Global data has background image URL:', !!window.storyGameData.backgroundImageUrl);
                  }
                  return; // Skip API generation since we have fresh data
                }
              } catch (parseError) {
                console.error('❌ Error parsing fresh story data:', parseError);
              }
            }

            // Check if complete game data (with base64 image) already exists in database
            // Add retry mechanism to handle race conditions
            let gameDataFound = false;
            let retryCount = 0;
            const maxRetries = 5;
            const retryDelay = 2000; // 2 seconds

            while (!gameDataFound && retryCount < maxRetries) {
              try {
                console.log(`🎮 Checking for complete game data with images... (attempt ${retryCount + 1}/${maxRetries})`);
                const gameDataResponse = await fetch(`/api/chats/${chatId}/gameData`);
                if (gameDataResponse.ok) {
                  const completeGameData = await gameDataResponse.json();
                  if (completeGameData && completeGameData.questions) {
                    console.log('✅ Found complete game data in database, loading...');
                    console.log(`- Has background image URL: ${!!completeGameData.backgroundImageUrl}`);
                    console.log(`- Background image URL: ${completeGameData.backgroundImageUrl || 'none'}`);
                    console.log(`- Chat ID: ${completeGameData.chatId || 'none'}`);
                    console.log(`- Questions count: ${completeGameData.questions?.length || 0}`);
                    console.log(`- Game data keys: ${Object.keys(completeGameData)}`);

                    // Test Firebase Storage image loading
                    if (completeGameData.backgroundImageUrl) {
                      testFirebaseImageLoading(completeGameData.backgroundImageUrl);
                    }

                    if (isMounted) {
                      setGameData(completeGameData);
                      setHasExistingStoryData(true);
                      setIsGenerating(false);
                      setUserPrompt(loadedPrompt);
                      setBookInfo(loadedBookInfo);
                    }

                    // Validate and store complete game data
                    if (validateStoryData(completeGameData)) {
                      // Store in localStorage for Godot game
                      localStorage.setItem('storyGameData', JSON.stringify(completeGameData));

                      // Also make it globally accessible for Godot
                      window.storyGameData = completeGameData;
                      console.log('✅ Complete game data loaded and made available globally');
                      console.log(`🖼️ Global data background image URL: ${!!window.storyGameData.backgroundImageUrl}`);
                      console.log(`🔗 Global data background URL: ${window.storyGameData.backgroundImageUrl || 'none'}`);
                    } else {
                      console.error('❌ Complete game data failed validation, not storing for Godot');
                    }
                    gameDataFound = true;
                    return; // Skip generation if we have existing data
                  }
                }
              } catch (gameDataError) {
                console.log(`⚠️ Attempt ${retryCount + 1} failed:`, gameDataError.message);
              }

              retryCount++;
              if (!gameDataFound && retryCount < maxRetries) {
                console.log(`⏳ Waiting ${retryDelay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
              }
            }

            if (!gameDataFound) {
              console.log('⚠️ No complete game data found after retries, checking for basic story data...');

              // Fallback to basic story data without images
              try {
                const storyDataResponse = await fetch(`/api/chats/${chatId}/storyData`);
                if (storyDataResponse.ok) {
                  const existingStoryData = await storyDataResponse.json();
                  if (existingStoryData && existingStoryData.questions) {
                    console.log('📄 Found basic story data in database (without images), loading...');
                    if (isMounted) {
                      setGameData(existingStoryData);
                      setHasExistingStoryData(true);
                      setIsGenerating(false);
                      setUserPrompt(loadedPrompt);
                      setBookInfo(loadedBookInfo);
                    }

                    // Validate and store basic story data
                    if (validateStoryData(existingStoryData)) {
                      // Store in localStorage for Godot game
                      localStorage.setItem('storyGameData', JSON.stringify(existingStoryData));

                      // Also make it globally accessible for Godot
                      window.storyGameData = existingStoryData;
                      console.log('📄 Basic story data loaded and made available globally (no background image)');
                    } else {
                      console.error('❌ Basic story data failed validation, not storing for Godot');
                    }
                    return; // Skip generation if we have existing data
                  }
                }
              } catch (storyDataError) {
                console.log('❌ No story data found in database, will generate new');
              }
            }

            // Set the loaded data
            if (isMounted) {
              setUserPrompt(loadedPrompt);
              setBookInfo(loadedBookInfo);
            }

            // Generate scenario if we have the required data
            if (loadedBookInfo.bookTitle && loadedPrompt && isMounted) {
              console.log('Generating scenario with loaded data:', { loadedBookInfo, loadedPrompt });
              await generateScenarioInternal(loadedBookInfo, loadedPrompt, chatId);
            }

          } catch (error) {
            console.error('Error loading chat data:', error);
            if (isMounted) {
              setError(`Failed to load analysis data: ${error.message}`);
              setIsGenerating(false);
            }
          } finally {
            if (isMounted) setIsLoadingFromAPI(false);
          }
        } else {
          // Fallback to localStorage method
          console.log('No chat ID found, using localStorage fallback');

          // First check if we already have generated story data from the prompt page
          const existingStoryData = localStorage.getItem('storyGameData');
          if (existingStoryData) {
            try {
              const parsedStoryData = JSON.parse(existingStoryData);
              if (parsedStoryData && parsedStoryData.questions && parsedStoryData.questions.length > 0) {
                console.log('Found existing story data from prompt page, using it directly');

                // Get the prompt and analysis results for display purposes
                const savedPrompt = localStorage.getItem('userPrompt');
                const savedResults = localStorage.getItem('analysisResults');

                if (savedResults && savedPrompt) {
                  const parsedResults = JSON.parse(savedResults);
                  const extractedBookInfo = extractBookInformation(parsedResults);

                  if (isMounted) {
                    setUserPrompt(savedPrompt);
                    setBookInfo(extractedBookInfo);
                    setGameData(parsedStoryData);
                    setIsGenerating(false);
                  }

                  // Validate and ensure story data is available globally
                  if (validateStoryData(parsedStoryData)) {
                    window.storyGameData = parsedStoryData;
                    console.log('Existing story data loaded and made available globally:', parsedStoryData);
                  }
                  return; // Skip API generation since we have fresh data
                }
              }
            } catch (parseError) {
              console.error('Error parsing existing story data:', parseError);
            }
          }

          // If no existing story data, fall back to analysis results
          const savedResults = localStorage.getItem('analysisResults');
          const savedPrompt = localStorage.getItem('userPrompt');

          if (!savedResults || !savedPrompt) {
            if (isMounted) {
              setError('No analysis results found. Please analyze a PDF first.');
              setIsGenerating(false);
            }
            return;
          }

          const parsedResults = JSON.parse(savedResults);
          const extractedBookInfo = extractBookInformation(parsedResults);

          if (isMounted) {
            setUserPrompt(savedPrompt);
            setBookInfo(extractedBookInfo);
          }

          // Generate scenario with localStorage data only if we don't have existing story data
          if (extractedBookInfo.bookTitle && savedPrompt && isMounted) {
            await generateScenarioInternal(extractedBookInfo, savedPrompt, null);
          }
        }

      } catch (error) {
        console.error('Error loading data and generating scenario:', error);
        if (isMounted) {
          setError('Error loading data: ' + (error.message || 'Unknown error'));
          setIsGenerating(false);
        }
      }
    }

    // Internal function to generate scenario (to avoid dependency issues)
    async function generateScenarioInternal(bookData, whatIfPrompt, chatId) {
      try {
        // Ensure we have the required data
        if (!bookData.bookTitle || !bookData.author || !whatIfPrompt) {
          if (isMounted) {
            setError('Missing required information to generate scenario.');
            setIsGenerating(false);
          }
          return;
        }

        console.log('Generating scenario with:', { bookData, whatIfPrompt });

        // Generate the game data using the API endpoint
        const response = await fetch('/api/alternate-scenario-game-data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            bookTitle: bookData.bookTitle,
            author: bookData.author,
            changeLocation: bookData.changeLocation,
            whatIfPrompt: whatIfPrompt,
            originalEvent: bookData.originalEvent
          }),
        });

        const result = await response.json();

        if (response.ok && result.questions && isMounted) {
          setGameData(result);

          // Validate story data format
          if (validateStoryData(result)) {
            // Store the game data in localStorage for the Godot game to access
            localStorage.setItem('storyGameData', JSON.stringify(result));

            // Also make it globally accessible for Godot
            window.storyGameData = result;
            console.log('Story data generated and made available globally:', result);
          } else {
            console.error('Generated story data failed validation, not storing for Godot');
          }

          // Save to chat if we have a chat ID
          if (chatId) {
            try {
              if (isMounted) setIsSavingStoryData(true);

              const saveResponse = await fetch(`/api/chats/${chatId}/storyData`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  ...result,
                  generatedAt: new Date().toISOString(),
                  prompt: whatIfPrompt,
                  bookInfo: bookData
                }),
              });

              if (saveResponse.ok) {
                const savedData = await saveResponse.json();
                if (isMounted) setStoryDataId(savedData.id);
                console.log('Story data saved successfully:', savedData.id);
              }
            } catch (saveError) {
              console.error('Error saving story data:', saveError);
              // Don't show error to user as this is not critical for gameplay
            } finally {
              if (isMounted) setIsSavingStoryData(false);
            }
          }

          console.log('Scenario generated successfully');
        } else if (isMounted) {
          setError(result.error || 'Failed to generate story data');
        }
      } catch (error) {
        console.error('Error generating scenario:', error);
        if (isMounted) {
          setError(error.message || 'An unexpected error occurred');
        }
      } finally {
        if (isMounted) setIsGenerating(false);
      }
    }

    loadDataAndGenerateScenario();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [isClient]); // Only depend on isClient to prevent multiple calls

  // Regenerate scenario function
  const regenerateScenario = useCallback(async () => {
    if (!bookInfo.bookTitle || !userPrompt) {
      setError('Missing required data to regenerate scenario');
      return;
    }

    setIsGenerating(true);
    setError('');
    setGameData(null);
    setGameLoaded(false);

    try {
      // Generate the game data using the API endpoint
      const response = await fetch('/api/alternate-scenario-game-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookTitle: bookInfo.bookTitle,
          author: bookInfo.author,
          changeLocation: bookInfo.changeLocation,
          whatIfPrompt: userPrompt,
          originalEvent: bookInfo.originalEvent
        }),
      });

      const result = await response.json();

      if (response.ok && result.questions) {
        setGameData(result);

        // Validate and store regenerated story data
        if (validateStoryData(result)) {
          // Store the game data in localStorage for the Godot game to access
          localStorage.setItem('storyGameData', JSON.stringify(result));

          // Also make it globally accessible for Godot
          window.storyGameData = result;
          console.log('Story data regenerated and made available globally:', result);
        } else {
          console.error('Regenerated story data failed validation, not storing for Godot');
        }

        // Save to chat if we have a chat ID
        if (currentChatId) {
          try {
            setIsSavingStoryData(true);

            const saveResponse = await fetch(`/api/chats/${currentChatId}/storyData`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                ...result,
                generatedAt: new Date().toISOString(),
                prompt: userPrompt,
                bookInfo: bookInfo
              }),
            });

            if (saveResponse.ok) {
              const savedData = await saveResponse.json();
              setStoryDataId(savedData.id);
              console.log('Story data saved successfully:', savedData.id);
            }
          } catch (saveError) {
            console.error('Error saving story data:', saveError);
          } finally {
            setIsSavingStoryData(false);
          }
        }

        console.log('Scenario regenerated successfully');
      } else {
        setError(result.error || 'Failed to generate story data');
      }
    } catch (error) {
      console.error('Error regenerating scenario:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  }, [bookInfo, userPrompt, currentChatId]);

  // Game initialization effect
  useEffect(() => {
    if (!gameData || !canvasRef.current || !containerRef.current) return;

    // Set canvas to maintain aspect ratio and fit within container
    const updateCanvasSize = () => {
      if (canvasRef.current && containerRef.current) {
        const container = containerRef.current;
        const rect = container.getBoundingClientRect();

        // Add some padding to ensure it fits comfortably
        const padding = 20;
        const availableWidth = Math.max(rect.width - padding, 300);
        const availableHeight = Math.max(rect.height - padding, 200);

        // Maintain 16:9 aspect ratio
        const aspectRatio = 16/9;

        let width = availableWidth;
        let height = width / aspectRatio;

        // If height is too large, constrain by height instead
        if (height > availableHeight) {
          height = availableHeight;
          width = height * aspectRatio;
        }

        // Ensure minimum viable size
        const minWidth = 400;
        const minHeight = 225; // 400/16*9

        if (width < minWidth) {
          width = Math.min(minWidth, availableWidth);
          height = width / aspectRatio;
        }

        if (height < minHeight) {
          height = Math.min(minHeight, availableHeight);
          width = height * aspectRatio;
        }

        // Final bounds check
        width = Math.min(width, availableWidth);
        height = Math.min(height, availableHeight);

        // Set the internal resolution (this affects game rendering quality)
        canvasRef.current.width = 1920; // Fixed internal resolution
        canvasRef.current.height = 1080; // Fixed internal resolution

        // Set the display size
        canvasRef.current.style.width = `${Math.floor(width)}px`;
        canvasRef.current.style.height = `${Math.floor(height)}px`;
        canvasRef.current.style.maxWidth = '100%';
        canvasRef.current.style.maxHeight = '100%';
        canvasRef.current.style.objectFit = 'contain';

        console.log(`Canvas sized to: ${Math.floor(width)}x${Math.floor(height)} (container: ${rect.width}x${rect.height})`);
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);

    let engine = null;

    // ✅ Define Module FIRST — critical
    window.Module = {
      locateFile: (path) => `/${path}`
    };

    const initializeGame = () => {
      const GODOT_CONFIG = {
        executable: 'index',
        canvasResizePolicy: 0, // 0 = None - we handle sizing ourselves
        fileSizes: {
          'index.pck': 97008,
          'index.wasm': 49282035
        },
        focusCanvas: true
      };

      engine = new window.Engine(GODOT_CONFIG);
      engineRef.current = engine;

      engine.startGame({
        canvas: canvasRef.current,
        onProgress: (current, total) => {
          if (total > 0) {
            setLoadingProgress((current / total) * 100);
          }
        }
      }).then(() => {
        setGameLoaded(true);

        // Pass story data to Godot game and preload background image
        try {
          const storyData = localStorage.getItem('storyGameData');
          if (storyData && window.Module && window.Module.ccall) {
            console.log('Passing story data to Godot game...');
            // Call a Godot function to receive the story data
            window.Module.ccall('receive_story_data', null, ['string'], [storyData]);
          } else {
            console.log('Story data or Godot Module not available yet, setting up global access...');
            // Make story data globally accessible for Godot
            window.storyGameData = JSON.parse(storyData || '{}');
          }

          // Preload background image for Godot
          if (window.GodotImageLoader) {
            window.GodotImageLoader.preloadBackgroundImage().then((base64Image) => {
              if (base64Image) {
                console.log('🖼️ Background image preloaded for Godot game');
              }
            }).catch((error) => {
              console.error('⚠️ Failed to preload background image:', error);
            });
          }

        } catch (error) {
          console.error('Error passing story data to Godot:', error);
          // Fallback: make data globally accessible
          try {
            const storyData = localStorage.getItem('storyGameData');
            window.storyGameData = JSON.parse(storyData || '{}');
          } catch (fallbackError) {
            console.error('Fallback also failed:', fallbackError);
          }
        }

        // Ensure proper sizing after game loads
        setTimeout(updateCanvasSize, 100);
      }).catch(err => {
        setGameError('Failed to start game: ' + err.message);
      });
    };

    // Since the script is loaded via layout, we can directly initialize
    if (window.Engine) {
      initializeGame();
    } else {
      // Fallback: wait for script to load
      const checkEngine = setInterval(() => {
        if (window.Engine) {
          clearInterval(checkEngine);
          initializeGame();
        }
      }, 100);

      // Cleanup interval after 10 seconds
      setTimeout(() => {
        clearInterval(checkEngine);
        if (!window.Engine) {
          setGameError('Failed to load game engine.');
        }
      }, 10000);
    }

    return () => {
      if (engineRef.current?.requestQuit) engineRef.current.requestQuit();
      window.removeEventListener('resize', updateCanvasSize);
    };
  }, [gameData]);

  if (!isClient) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-gray-900 p-4 flex items-center justify-between flex-shrink-0">
        <div className="flex items-center space-x-4">
          <Link
            href={currentChatId ? `/chat/${currentChatId}` : "/dashboard"}
            className="flex items-center text-white hover:text-gray-300 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {currentChatId ? 'Back to Chat' : 'Back to Dashboard'}
          </Link>
          
          {currentChatId && (
            <span className="text-gray-400 text-sm">
              Chat: {currentChatId}
            </span>
          )}
        </div>

        <h1 className="text-white text-xl font-bold">Alternate Timeline Story</h1>

        <div className="flex items-center space-x-2">
          {/* Save Status */}
          {isSavingStoryData && (
            <div className="flex items-center text-blue-400 text-sm">
              <Save className="w-4 h-4 mr-1 animate-pulse" />
              Saving...
            </div>
          )}
          
          {storyDataId && !isSavingStoryData && (
            <div className="flex items-center text-green-400 text-sm">
              <Save className="w-4 h-4 mr-1" />
              Saved
            </div>
          )}

          {/* Regenerate Button */}
          {!isGenerating && gameData && (
            <div className="flex gap-2">
              <button
                onClick={regenerateScenario}
                className="flex items-center text-white hover:text-gray-300 transition-colors bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm"
              >
                <RefreshCw className="w-4 h-4 mr-1" />
                Regenerate
              </button>

              {/* Debug button for Godot integration */}
              <button
                onClick={() => {
                  console.log('=== Manual Godot Data Check ===');
                  const storyData = localStorage.getItem('storyGameData');
                  console.log('Story data in localStorage:', storyData);
                  console.log('Story data on window:', window.storyGameData);

                  if (storyData) {
                    try {
                      const parsed = JSON.parse(storyData);
                      console.log('Parsed story data:', parsed);
                      console.log('Questions count:', parsed.questions?.length || 0);

                      // Try to pass data to Godot if available
                      if (window.Module && window.Module.ccall) {
                        console.log('Attempting to call Godot function...');
                        try {
                          window.Module.ccall('receive_story_data', null, ['string'], [storyData]);
                          console.log('Successfully called Godot function');
                        } catch (godotError) {
                          console.error('Error calling Godot function:', godotError);
                        }
                      } else {
                        console.log('Godot Module not available');
                      }
                    } catch (parseError) {
                      console.error('Error parsing story data:', parseError);
                    }
                  } else {
                    console.log('No story data found');
                  }
                  console.log('=== End Debug ===');
                }}
                className="flex items-center text-white hover:text-gray-300 transition-colors bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"
              >
                Debug
              </button>

              {/* Test Image Loading button */}
              <button
                onClick={() => {
                  console.log('=== Manual Image Loading Test ===');
                  const storyData = localStorage.getItem('storyGameData');
                  if (storyData) {
                    try {
                      const parsed = JSON.parse(storyData);
                      if (parsed.backgroundImageUrl) {
                        console.log('Testing background image URL:', parsed.backgroundImageUrl);
                        testFirebaseImageLoading(parsed.backgroundImageUrl);
                      } else {
                        console.log('No background image URL found in story data');
                      }
                    } catch (parseError) {
                      console.error('Error parsing story data:', parseError);
                    }
                  } else {
                    console.log('No story data found in localStorage');
                  }
                  console.log('=== End Image Test ===');
                }}
                className="flex items-center text-white hover:text-gray-300 transition-colors bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm"
              >
                Test Image
              </button>

              {/* Test Godot Bridge button */}
              <button
                onClick={async () => {
                  console.log('=== Godot Bridge Test ===');
                  if (window.GodotImageLoader) {
                    try {
                      console.log('Testing Godot Image Bridge...');
                      const base64Image = await window.GodotImageLoader.preloadBackgroundImage();
                      if (base64Image) {
                        console.log('✅ Godot Bridge test successful!');
                        console.log('📊 Base64 image length:', base64Image.length);
                        console.log('🌍 Preloaded image available at window.preloadedBackgroundImage');
                      } else {
                        console.log('⚠️ No background image to preload');
                      }
                    } catch (error) {
                      console.error('❌ Godot Bridge test failed:', error);
                    }
                  } else {
                    console.error('❌ Godot Image Bridge not available');
                  }
                  console.log('=== End Bridge Test ===');
                }}
                className="flex items-center text-white hover:text-gray-300 transition-colors bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-sm"
              >
                Test Bridge
              </button>

              {/* Status Check button */}
              <button
                onClick={() => {
                  console.log('=== Firebase Storage Image Status ===');

                  if (window.GodotImageLoader) {
                    const status = window.GodotImageLoader.getStatus();
                    console.log('📊 Bridge Status:', status);

                    if (status.backgroundImageUrl) {
                      const validation = window.GodotImageLoader.validateImageUrl(status.backgroundImageUrl);
                      console.log('🔍 URL Validation:', validation);
                    }

                    console.log('🖼️ Preloaded Image Available:', status.preloadedImageAvailable);
                    console.log('📦 Cache Size:', status.cacheSize);
                    console.log('🔗 Cached URLs:', status.cachedUrls);

                    // Test current image URL if available
                    if (status.backgroundImageUrl && status.backgroundImageUrl.trim()) {
                      console.log('🧪 Testing current background image URL...');
                      testFirebaseImageLoading(status.backgroundImageUrl);
                    } else {
                      console.log('⚠️ No background image URL to test');
                    }
                  } else {
                    console.error('❌ Godot Image Bridge not available');
                  }

                  console.log('=== End Status Check ===');
                }}
                className="flex items-center text-white hover:text-gray-300 transition-colors bg-orange-600 hover:bg-orange-700 px-3 py-1 rounded text-sm"
              >
                Status
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Loading State - Full Screen */}
      {(isGenerating || isLoadingFromAPI) && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-white max-w-md">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-lg mb-2">
              {isLoadingFromAPI 
                ? 'Loading your story analysis...' 
                : hasExistingStoryData 
                ? 'Loading existing story...'
                : 'Generating your interactive story...'}
            </p>
            <p className="text-sm text-gray-400">
              {isLoadingFromAPI 
                ? 'Fetching data from your analysis session'
                : hasExistingStoryData
                ? 'Found existing story data in your account'
                : 'Creating personalized alternate timeline scenarios'}
            </p>
            
            {currentChatId && (
              <p className="text-xs text-gray-500 mt-2">
                Session: {currentChatId}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Error State - Full Screen */}
      {error && (
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-red-900/50 border border-red-700 p-6 rounded-lg text-center max-w-md">
            <h2 className="text-xl font-bold mb-2 text-white">Error</h2>
            <p className="mb-4 text-red-200">{error}</p>
            <div className="space-y-2">
              {currentChatId ? (
                <Link
                  href={`/chat/${currentChatId}`}
                  className="block px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Return to Chat
                </Link>
              ) : (
                <Link
                  href="/dashboard"
                  className="block px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Return to Dashboard
                </Link>
              )}
              
              {bookInfo.bookTitle && userPrompt && (
                <button
                  onClick={regenerateScenario}
                  className="block w-full px-6 py-3 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  Try Again
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Story Info Panel - Show when we have data */}
      {(bookInfo.bookTitle || userPrompt) && !error && !isGenerating && !isLoadingFromAPI && (
        <div className="bg-gray-800 border-b border-gray-700 p-4 text-sm">
          <div className="flex flex-wrap items-center justify-between max-w-6xl mx-auto">
            <div className="flex flex-wrap items-center space-x-4 text-gray-300">
              {bookInfo.bookTitle && (
                <span><strong>Story:</strong> {bookInfo.bookTitle}</span>
              )}
              {bookInfo.author && bookInfo.author !== 'Unknown Author' && (
                <span><strong>Author:</strong> {bookInfo.author}</span>
              )}
              {bookInfo.changeLocation && (
                <span><strong>Section:</strong> {bookInfo.changeLocation}</span>
              )}
            </div>
            {userPrompt && (
              <div className="text-gray-400 max-w-md truncate">
                <strong>Prompt:</strong> {userPrompt}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Game Container */}
      {!isGenerating && !isLoadingFromAPI && !error && gameData && (
        <div className="flex-1 flex flex-col overflow-hidden min-h-0">
          <div ref={containerRef} className="flex-1 relative flex items-center justify-center bg-gray-900 min-h-0 w-full">
            {/* Loading Screen */}
            {!gameLoaded && !gameError && (
              <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                <div className="text-white text-xl mb-4">Loading Interactive Story...</div>
                <div className="w-64 h-2 bg-gray-600 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-500 transition-all duration-300"
                    style={{ width: `${loadingProgress}%` }}
                  />
                </div>
                <div className="text-gray-300 mt-2">{Math.round(loadingProgress)}%</div>
                
                {hasExistingStoryData && (
                  <div className="text-green-400 text-sm mt-2">
                    ✓ Loaded from your saved progress
                  </div>
                )}
              </div>
            )}

            {/* Error Screen */}
            {gameError && (
              <div className="absolute inset-0 flex flex-col items-center justify-center z-10">
                <div className="text-white text-xl mb-4">Error Loading Game</div>
                <div className="text-red-200 text-center max-w-md mb-4">
                  {gameError}
                </div>
                <div className="space-x-4">
                  <button
                    onClick={() => window.location.reload()}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                  >
                    Retry
                  </button>
                  {currentChatId ? (
                    <Link
                      href={`/chat/${currentChatId}`}
                      className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                    >
                      Back to Chat
                    </Link>
                  ) : (
                    <Link
                      href="/dashboard"
                      className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                    >
                      Back to Dashboard
                    </Link>
                  )}
                </div>
              </div>
            )}

            {/* Game Canvas */}
            <canvas
              ref={canvasRef}
              id="canvas"
              className="block"
              style={{
                imageRendering: 'pixelated',
                imageRendering: '-moz-crisp-edges',
                imageRendering: 'crisp-edges',
              }}
            >
              HTML5 canvas appears to be unsupported in the current browser.
              Please try updating or use a different browser.
            </canvas>
          </div>
        </div>
      )}
    </div>
  );
}
