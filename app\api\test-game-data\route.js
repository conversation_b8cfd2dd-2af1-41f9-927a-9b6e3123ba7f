import { NextResponse } from 'next/server';

// Test endpoint to simulate game data for testing the game-loader flow
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');

    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    // Simulate game data with a test image URL
    const testGameData = {
      chatId: chatId,
      backgroundImageUrl: 'https://via.placeholder.com/800x400/4F46E5/FFFFFF?text=Test+Background+Image',
      questions: [
        {
          node_id: 'test_1',
          title: 'Test Scenario 1',
          question: 'What if the protagonist had made a different choice?',
          options: [
            'Option A: Take the safe path',
            'Option B: Take the risky adventure',
            'Option C: Seek help from others'
          ],
          level: 1,
          imageUrl: 'https://via.placeholder.com/600x300/8B5CF6/FFFFFF?text=Scenario+1'
        },
        {
          node_id: 'test_2',
          title: 'Test Scenario 2',
          question: 'How would the story change if the setting was different?',
          options: [
            'Option A: Modern city setting',
            'Option B: Rural countryside',
            'Option C: Futuristic world'
          ],
          level: 2,
          imageUrl: 'https://via.placeholder.com/600x300/10B981/FFFFFF?text=Scenario+2'
        }
      ],
      metadata: {
        bookTitle: 'Test Story',
        author: 'Test Author',
        generatedAt: new Date().toISOString(),
        totalScenarios: 2
      }
    };

    console.log(`📋 Test game data generated for chat: ${chatId}`);

    return NextResponse.json(testGameData);

  } catch (error) {
    console.error('❌ Error generating test game data:', error);
    return NextResponse.json(
      { error: 'Failed to generate test game data' },
      { status: 500 }
    );
  }
}
