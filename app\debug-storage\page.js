'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

export default function DebugStorage() {
  const [storageData, setStorageData] = useState({});
  const [imagePreview, setImagePreview] = useState(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    loadStorageData();
  }, []);

  const loadStorageData = () => {
    if (typeof window === 'undefined') return;

    const data = {};
    const keys = Object.keys(localStorage);
    
    keys.forEach(key => {
      if (key.startsWith('game') || key === 'storyGameData' || key === 'currentChatId') {
        const value = localStorage.getItem(key);
        
        if (key.includes('Image') && value && value.startsWith('data:image/')) {
          data[key] = {
            type: 'image',
            size: value.length,
            preview: value.substring(0, 100) + '...',
            full: value
          };
        } else {
          data[key] = {
            type: 'text',
            size: value ? value.length : 0,
            value: value
          };
        }
      }
    });

    setStorageData(data);
  };

  const showImagePreview = (base64Data) => {
    setImagePreview(base64Data);
  };

  const clearStorage = () => {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('game') || key === 'storyGameData' || key === 'currentChatId') {
        localStorage.removeItem(key);
      }
    });
    loadStorageData();
    setImagePreview(null);
  };

  const testGodotBridge = () => {
    if (typeof window === 'undefined') return;

    console.log('=== Testing Godot Bridge ===');
    
    if (window.GodotImageLoader) {
      console.log('✅ GodotImageLoader is available');
      
      const status = window.GodotImageLoader.getStatus();
      console.log('📊 Bridge Status:', status);
      
      const chatId = localStorage.getItem('currentChatId');
      if (chatId) {
        const localImage = window.GodotImageLoader.loadImageFromLocalStorage(chatId);
        console.log('🖼️ Local image available:', !!localImage);
        if (localImage) {
          console.log('📏 Image size:', localImage.length);
        }
      }
    } else {
      console.log('❌ GodotImageLoader not available');
    }
  };

  if (!isClient) {
    return <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
      <div>Loading...</div>
    </div>;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">localStorage Debug Panel</h1>
        
        <div className="mb-6 space-x-4">
          <button
            onClick={loadStorageData}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Refresh Data
          </button>
          
          <button
            onClick={clearStorage}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Clear Game Storage
          </button>
          
          <button
            onClick={testGodotBridge}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Test Godot Bridge
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Storage Data */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Storage Contents</h2>
            
            {Object.keys(storageData).length === 0 ? (
              <p className="text-gray-400">No game-related data found in localStorage</p>
            ) : (
              <div className="space-y-4">
                {Object.entries(storageData).map(([key, data]) => (
                  <div key={key} className="border border-gray-700 rounded p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-blue-300">{key}</h3>
                      <span className="text-xs text-gray-400">
                        {data.type} ({data.size} chars)
                      </span>
                    </div>
                    
                    {data.type === 'image' ? (
                      <div>
                        <p className="text-sm text-gray-300 mb-2">Base64 Image Data</p>
                        <button
                          onClick={() => showImagePreview(data.full)}
                          className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700"
                        >
                          Preview Image
                        </button>
                      </div>
                    ) : (
                      <div>
                        <pre className="text-sm bg-gray-900 p-2 rounded overflow-auto max-h-32">
                          {data.value ? (
                            data.value.length > 500 
                              ? data.value.substring(0, 500) + '...'
                              : data.value
                          ) : 'null'}
                        </pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Image Preview */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Image Preview</h2>
            
            {imagePreview ? (
              <div>
                <div className="mb-4">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="max-w-full h-auto rounded border border-gray-600"
                    style={{ maxHeight: '400px' }}
                  />
                </div>
                <button
                  onClick={() => setImagePreview(null)}
                  className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
                >
                  Close Preview
                </button>
              </div>
            ) : (
              <p className="text-gray-400">No image selected for preview</p>
            )}
          </div>
        </div>

        {/* Test Links */}
        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Links</h2>
          <div className="space-y-2">
            <div>
              <a
                href="/game-loader?chatId=test123"
                className="text-blue-400 hover:text-blue-300 underline"
              >
                Test Game Loader (test123)
              </a>
              <span className="text-gray-400 ml-2">- Load test game data and images</span>
            </div>
            <div>
              <a
                href="/alternate-scenario?chatId=test123"
                className="text-blue-400 hover:text-blue-300 underline"
              >
                Direct to Game (test123)
              </a>
              <span className="text-gray-400 ml-2">- Skip loader, go directly to game</span>
            </div>
            <div>
              <a
                href="/test-game-loader"
                className="text-blue-400 hover:text-blue-300 underline"
              >
                Test Suite
              </a>
              <span className="text-gray-400 ml-2">- Run comprehensive tests</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
